package com.flansmod.fix;

import cpw.mods.fml.relauncher.IFMLLoadingPlugin;
import java.util.Map;

/**
 * <PERSON>lan's Mod 修复核心插件
 * 用于注册 ASM 转换器
 */
@IFMLLoadingPlugin.MCVersion("1.7.10")
@IFMLLoadingPlugin.TransformerExclusions({"com.flansmod.fix"})
public class FlanMod<PERSON>ix<PERSON>ore implements IFMLLoadingPlugin {
    
    @Override
    public String[] getASMTransformerClass() {
        return new String[]{"com.flansmod.fix.asm.GunTypeTransformer"};
    }
    
    @Override
    public String getModContainerClass() {
        return null;
    }
    
    @Override
    public String getSetupClass() {
        return null;
    }
    
    @Override
    public void injectData(Map<String, Object> data) {
        // 不需要注入数据
    }
    
    @Override
    public String getAccessTransformerClass() {
        return null;
    }
}
