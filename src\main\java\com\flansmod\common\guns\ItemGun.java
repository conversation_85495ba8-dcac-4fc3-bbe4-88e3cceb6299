package com.flansmod.common.guns;

import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.EnumAction;

/**
 * 修复版本的 ItemGun 类
 * 主要修复：在调用 GunType.getStates() 之前添加安全检查
 */
public class ItemGun extends Item {
    
    private GunType gunType;
    
    /**
     * 修复后的 getItemUseAction 方法
     * 原始错误出现在第 970 行，调用 gunType.getStates() 时
     */
    @Override
    public EnumAction getItemUseAction(ItemStack itemStack) {
        // 修复：添加安全检查
        if (itemStack == null) {
            return EnumAction.none;
        }
        
        if (gunType == null) {
            return EnumAction.none;
        }
        
        try {
            // 现在安全地调用 getStates
            Object states = gunType.getStates(itemStack);
            
            // 根据状态返回相应的动作
            if (states != null) {
                return EnumAction.bow; // 或其他适当的动作
            } else {
                return EnumAction.none;
            }
        } catch (Exception e) {
            // 额外的错误处理
            System.err.println("Error in ItemGun.getItemUseAction: " + e.getMessage());
            return EnumAction.none;
        }
    }
    
    /**
     * 安全的初始化方法
     */
    public void setGunType(GunType type) {
        this.gunType = type;
    }
    
    // 其他方法保持不变...
}
