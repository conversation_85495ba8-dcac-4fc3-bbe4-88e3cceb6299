# <PERSON><PERSON>'s Mod 修复补丁

这个模组修复了 <PERSON><PERSON>'s Mod 与 Spice of Life 模组之间的兼容性问题。

## 问题描述

原始错误：
```
java.lang.NullPointerException: Cannot invoke "net.minecraft.nbt.NBTTagCompound.func_74781_a(String)" because "gunStack.field_77990_d" is null
```

错误发生在：
- `GunType.getStates()` 方法第 945 行
- 当 `ItemStack` 的 NBT 数据为 null 时尝试访问

## 修复方案

本补丁提供了多种修复方案：

### 1. 反射修复器 (推荐)
- 运行时检测和修复
- 不修改原始字节码
- 最安全的方案

### 2. ASM 字节码修复
- 直接修补 GunType 类
- 在方法开始处添加 null 检查

### 3. 替换类修复
- 提供修复后的 GunType 和 ItemGun 类
- 完全替换有问题的方法

## 安装方法

1. 编译这个模组：
   ```bash
   ./gradlew build
   ```

2. 将生成的 jar 文件放入 mods 文件夹

3. 重启 Minecraft

## 修复内容

- 在 `GunType.getStates()` 中添加 null 检查
- 在 `ItemGun.getItemUseAction()` 中添加安全检查
- 提供安全的 NBT 数据访问方法

## 兼容性

- Minecraft 1.7.10
- Forge 10.13.4.1614
- Flan's Mod (所有版本)
- Spice of Life
- GT New Horizons 2.7.4

## 技术细节

修复的核心逻辑：
```java
// 修复前（会崩溃）
return gunStack.stackTagCompound.getString("gunStates");

// 修复后（安全）
if (gunStack == null || gunStack.stackTagCompound == null) {
    return "default";
}
return gunStack.stackTagCompound.getString("gunStates");
```
