package com.flansmod.fix;

import cpw.mods.fml.common.Mod;
import cpw.mods.fml.common.event.FMLPreInitializationEvent;
import cpw.mods.fml.common.event.FMLInitializationEvent;
import cpw.mods.fml.common.event.FMLPostInitializationEvent;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;

/**
 * <PERSON>lan's Mod 修复补丁
 * 修复 NullPointerException 问题
 */
@Mod(modid = FlanModFix.MODID, version = FlanModFix.VERSION, name = "<PERSON>lan's Mod Fix")
public class FlanModFix {
    public static final String MODID = "flansmodfix";
    public static final String VERSION = "1.0.0";
    
    @Mod.EventHandler
    public void preInit(FMLPreInitializationEvent event) {
        System.out.println("Flan's Mod Fix: Pre-initialization");
    }
    
    @Mod.EventHandler
    public void init(FMLInitializationEvent event) {
        System.out.println("Flan's Mod Fix: Initialization");
    }
    
    @Mod.EventHandler
    public void postInit(FMLPostInitializationEvent event) {
        System.out.println("Flan's Mod Fix: Post-initialization");

        // 初始化反射修复器
        ReflectionFixer.init();

        System.out.println("Flan's Mod Fix: Applied null pointer fixes");
    }
    
    /**
     * 安全的 NBT 数据访问方法
     * 用于替换原始的不安全访问
     */
    public static String safeGetStringFromNBT(ItemStack itemStack, String key) {
        if (itemStack == null) {
            return "";
        }
        
        NBTTagCompound nbt = itemStack.stackTagCompound;
        if (nbt == null) {
            return "";
        }
        
        try {
            return nbt.getString(key);
        } catch (Exception e) {
            System.err.println("Error accessing NBT data: " + e.getMessage());
            return "";
        }
    }
    
    /**
     * 安全的 NBT 数据检查方法
     */
    public static boolean hasNBTData(ItemStack itemStack) {
        return itemStack != null && itemStack.stackTagCompound != null;
    }
}
