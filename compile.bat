@echo off
echo Compiling Flan's Mod Fix...

REM 创建输出目录
if not exist "build\classes" mkdir "build\classes"
if not exist "build\libs" mkdir "build\libs"

REM 编译 Java 源代码
echo Compiling Java sources...
javac -cp "." -d "build\classes" src\main\java\com\flansmod\fix\*.java src\main\java\com\flansmod\fix\asm\*.java src\main\java\com\flansmod\common\guns\*.java

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

REM 复制资源文件
echo Copying resources...
xcopy "src\main\resources\*" "build\classes\" /E /Y

REM 创建 JAR 文件
echo Creating JAR file...
cd build\classes
jar cfm "..\libs\flansmod-fix-1.0.0.jar" "META-INF\MANIFEST.MF" .
cd ..\..

echo Build completed! JAR file created at: build\libs\flansmod-fix-1.0.0.jar
echo.
echo Installation instructions:
echo 1. Copy the JAR file to your mods folder
echo 2. Restart Minecraft
echo 3. The fix should prevent the NullPointerException
echo.
pause
