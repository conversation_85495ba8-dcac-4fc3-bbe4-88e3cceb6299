@echo off
echo Building Flan's Mod with NullPointerException fix...
echo.

REM Try to build with gradlew
echo Attempting to build with Gradle...
gradlew.bat build

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! 
    echo Check the build/libs/ directory for the compiled jar file.
    echo.
    echo To use the fixed mod:
    echo 1. Find the jar file in build/libs/
    echo 2. Replace your current Flan's Mod jar in the mods folder
    echo 3. Start Minecraft
    echo.
    pause
) else (
    echo.
    echo Build failed. This might be due to network issues.
    echo.
    echo Alternative solutions:
    echo 1. Try building again later when network is stable
    echo 2. Use a VPN if you're in a restricted network
    echo 3. Apply the fixes manually to your existing Flan's Mod source
    echo.
    echo Manual fix instructions:
    echo 1. Open your Flan's Mod source code
    echo 2. Find GunType.java and replace the getStates() method with the fixed version
    echo 3. Find ItemGun.java and replace the getItemUseAction() method with the fixed version
    echo 4. Compile using your preferred method
    echo.
    echo See FLAN_MOD_FIX_README.md for detailed instructions.
    echo.
    pause
)
