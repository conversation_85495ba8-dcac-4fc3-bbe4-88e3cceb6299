diff --git a/src/main/java/com/flansmod/common/guns/GunType.java b/src/main/java/com/flansmod/common/guns/GunType.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/flansmod/common/guns/GunType.java
+++ b/src/main/java/com/flansmod/common/guns/GunType.java
@@ -942,7 +942,21 @@ public class GunType extends InfoType implements IRecipeElement
 	/* methods to handle gun states MARK */
 	public static boolean hasStates(ItemStack stack) { return stack.stackTagCompound != null && stack.stackTagCompound.hasKey(ModifiableTag.STATES); }
 	
-	public static int[] getStates(ItemStack gunStack) { return ((NBTTagIntArray)gunStack.stackTagCompound.getTag(ModifiableTag.STATES)).func_150302_c(); }
+	public static int[] getStates(ItemStack gunStack) { 
+		// Fix for NullPointerException when stackTagCompound is null
+		if (gunStack == null || gunStack.stackTagCompound == null) {
+			// Return a default states array to prevent crashes
+			// This should match the expected array size for gun states
+			return new int[GunTag.values().length];
+		}
+		
+		// Check if the STATES tag exists
+		if (!gunStack.stackTagCompound.hasKey(ModifiableTag.STATES)) {
+			// Return default states array if no states tag exists
+			return new int[GunTag.values().length];
+		}
+		
+		try {
+			return ((NBTTagIntArray)gunStack.stackTagCompound.getTag(ModifiableTag.STATES)).func_150302_c();
+		} catch (Exception e) {
+			// If there's any error reading the NBT data, return default states
+			System.err.println("Flan's Mod: Error reading gun states from NBT: " + e.getMessage());
+			return new int[GunTag.values().length];
+		}
+	}
 	
 	public static AttachmentType getMuzzleType(int[] states)
 	{ return states[GunTag.MUZZLE_GRIP] < 0 ? null : ((ItemAttachment)Item.getItemById(states[GunTag.MUZZLE_GRIP] >>> 16)).type; }

diff --git a/src/main/java/com/flansmod/common/guns/ItemGun.java b/src/main/java/com/flansmod/common/guns/ItemGun.java
index 1234567..abcdefg 100644
--- a/src/main/java/com/flansmod/common/guns/ItemGun.java
+++ b/src/main/java/com/flansmod/common/guns/ItemGun.java
@@ -967,7 +967,18 @@ public class ItemGun extends ItemShootable implements IRecipeElement
 	
 	@Override
 	public EnumAction getItemUseAction(ItemStack stack)
-	{ return (GunType.getTOF(GunType.getStates(stack), GunTag.RUN_POSE_ON) ? EnumAction.none : type.itemUseAction); }
+	{ 
+		// Fix for NullPointerException when called by other mods (like Spice of Life via AppleCore)
+		if (stack == null || type == null) {
+			return EnumAction.none;
+		}
+		
+		// Safe call to getStates - now handles null NBT data properly
+		try {
+			return (GunType.getTOF(GunType.getStates(stack), GunTag.RUN_POSE_ON) ? EnumAction.none : type.itemUseAction);
+		} catch (Exception e) {
+			// If there's any error, return a safe default
+			System.err.println("Flan's Mod: Error in getItemUseAction: " + e.getMessage());
+			return EnumAction.none;
+		}
+	}
 	
 	@Override
 	public Multimap getAttributeModifiers(ItemStack stack)
