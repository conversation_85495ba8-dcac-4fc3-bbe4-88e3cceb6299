package com.flansmod.fix.asm;

import net.minecraft.launchwrapper.IClassTransformer;
import org.objectweb.asm.*;
import org.objectweb.asm.tree.*;

/**
 * ASM 类转换器，用于修补 GunType 类的 getStates 方法
 * 在方法开始处添加 null 检查
 */
public class GunTypeTransformer implements IClassTransformer {
    
    @Override
    public byte[] transform(String name, String transformedName, byte[] basicClass) {
        // 只处理 GunType 类
        if (!"com.flansmod.common.guns.GunType".equals(transformedName)) {
            return basicClass;
        }
        
        System.out.println("Flan's Mod Fix: Transforming GunType class");
        
        try {
            ClassNode classNode = new ClassNode();
            ClassReader classReader = new ClassReader(basicClass);
            classReader.accept(classNode, 0);
            
            // 查找 getStates 方法
            for (MethodNode method : classNode.methods) {
                if ("getStates".equals(method.name)) {
                    System.out.println("<PERSON>lan's Mod Fix: Found getStates method, applying fix");
                    transformGetStatesMethod(method);
                }
            }
            
            ClassWriter classWriter = new ClassWriter(ClassWriter.COMPUTE_MAXS);
            classNode.accept(classWriter);
            return classWriter.toByteArray();
            
        } catch (Exception e) {
            System.err.println("Flan's Mod Fix: Error transforming GunType: " + e.getMessage());
            e.printStackTrace();
            return basicClass;
        }
    }
    
    /**
     * 转换 getStates 方法，添加 null 检查
     */
    private void transformGetStatesMethod(MethodNode method) {
        InsnList instructions = new InsnList();
        
        // 添加 null 检查代码
        // if (gunStack == null) return getDefaultStates();
        instructions.add(new VarInsnNode(Opcodes.ALOAD, 1)); // 加载 gunStack 参数
        LabelNode notNullLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFNONNULL, notNullLabel));
        
        // 如果为 null，返回默认值
        instructions.add(new LdcInsnNode("default")); // 加载默认字符串
        instructions.add(new InsnNode(Opcodes.ARETURN));
        
        instructions.add(notNullLabel);
        
        // 检查 NBT 数据
        // if (gunStack.stackTagCompound == null) return getDefaultStates();
        instructions.add(new VarInsnNode(Opcodes.ALOAD, 1)); // 加载 gunStack
        instructions.add(new FieldInsnNode(Opcodes.GETFIELD, 
            "net/minecraft/item/ItemStack", "field_77990_d", // stackTagCompound 的混淆名
            "Lnet/minecraft/nbt/NBTTagCompound;"));
        
        LabelNode nbtNotNullLabel = new LabelNode();
        instructions.add(new JumpInsnNode(Opcodes.IFNONNULL, nbtNotNullLabel));
        
        // 如果 NBT 为 null，返回默认值
        instructions.add(new LdcInsnNode("default"));
        instructions.add(new InsnNode(Opcodes.ARETURN));
        
        instructions.add(nbtNotNullLabel);
        
        // 在原方法开始处插入我们的检查
        method.instructions.insertBefore(method.instructions.getFirst(), instructions);
    }
}
