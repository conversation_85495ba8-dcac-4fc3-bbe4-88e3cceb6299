package com.flansmod.common.guns;

import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;

/**
 * 修复版本的 GunType 类
 * 主要修复：在 getStates() 方法中添加 null 检查，防止 NullPointerException
 */
public class GunType {
    
    // 其他字段和方法...
    
    /**
     * 修复后的 getStates 方法
     * 原始错误：gunStack.field_77990_d 为 null 时调用方法导致 NPE
     * 修复：添加 null 检查
     */
    public Object getStates(ItemStack gunStack) {
        // 修复：检查 ItemStack 是否为 null
        if (gunStack == null) {
            return getDefaultStates(); // 返回默认状态
        }
        
        // 修复：检查 NBT 数据是否为 null
        // field_77990_d 是 ItemStack.stackTagCompound 的混淆名称
        NBTTagCompound nbtData = gunStack.stackTagCompound;
        if (nbtData == null) {
            // 如果没有 NBT 数据，创建默认的或返回默认状态
            return getDefaultStates();
        }
        
        // 原始代码逻辑（现在安全了）
        try {
            // 这里是原来第 945 行的代码，现在有了 null 检查
            return nbtData.getString("gunStates"); // func_74781_a 的反混淆版本
        } catch (Exception e) {
            // 额外的错误处理
            System.err.println("Error reading gun states from NBT: " + e.getMessage());
            return getDefaultStates();
        }
    }
    
    /**
     * 返回默认的枪械状态
     */
    private Object getDefaultStates() {
        // 返回一个安全的默认状态
        return "default";
    }
    
    // 其他方法保持不变...
}
