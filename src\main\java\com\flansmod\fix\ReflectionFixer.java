package com.flansmod.fix;

import cpw.mods.fml.common.FMLCommonHandler;
import cpw.mods.fml.common.eventhandler.SubscribeEvent;
import cpw.mods.fml.common.gameevent.TickEvent;
import net.minecraft.item.ItemStack;
import net.minecraft.nbt.NBTTagCompound;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 反射修复器 - 运行时修复 Flan's Mod 的问题
 * 这是一个更安全的修复方案，不需要修改字节码
 */
public class ReflectionFixer {
    
    private static boolean fixApplied = false;
    private static Class<?> gunTypeClass;
    private static Method getStatesMethod;
    
    public static void init() {
        FMLCommonHandler.instance().bus().register(new ReflectionFixer());
        System.out.println("Flan's Mod Fix: Reflection fixer initialized");
    }
    
    @SubscribeEvent
    public void onServerTick(TickEvent.ServerTickEvent event) {
        if (!fixApplied && event.phase == TickEvent.Phase.START) {
            applyFix();
        }
    }
    
    /**
     * 应用修复
     */
    private void applyFix() {
        try {
            // 尝试获取 GunType 类
            gunTypeClass = Class.forName("com.flansmod.common.guns.GunType");
            getStatesMethod = gunTypeClass.getDeclaredMethod("getStates", ItemStack.class);
            getStatesMethod.setAccessible(true);
            
            System.out.println("Flan's Mod Fix: Successfully located GunType.getStates method");
            fixApplied = true;
            
        } catch (Exception e) {
            System.err.println("Flan's Mod Fix: Could not apply reflection fix: " + e.getMessage());
        }
    }
    
    /**
     * 安全调用 getStates 方法
     */
    public static Object safeGetStates(Object gunType, ItemStack itemStack) {
        if (gunType == null || itemStack == null) {
            return "default";
        }
        
        // 检查 NBT 数据
        if (itemStack.stackTagCompound == null) {
            return "default";
        }
        
        try {
            if (getStatesMethod != null) {
                return getStatesMethod.invoke(gunType, itemStack);
            }
        } catch (Exception e) {
            System.err.println("Flan's Mod Fix: Error in safeGetStates: " + e.getMessage());
        }
        
        return "default";
    }
    
    /**
     * 创建安全的 NBT 数据
     */
    public static void ensureNBTData(ItemStack itemStack) {
        if (itemStack != null && itemStack.stackTagCompound == null) {
            itemStack.stackTagCompound = new NBTTagCompound();
        }
    }
}
