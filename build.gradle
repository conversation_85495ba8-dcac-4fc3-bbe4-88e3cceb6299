buildscript {
    repositories {
        mavenCentral()
        maven {
            name = "forge"
            url = "https://maven.minecraftforge.net/"
        }
        maven {
            name = "sonatype"
            url = "https://oss.sonatype.org/content/repositories/snapshots/"
        }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:1.2-SNAPSHOT'
    }
}

apply plugin: 'forge'

version = "1.0.0-fixed"
group = "com.flansmod.fixed"
archivesBaseName = "flansmod-fixed"

minecraft {
    version = "1.7.10-10.13.4.1614-1.7.10"
    runDir = "eclipse"
}

repositories {
    maven {
        name = "chickenbones"
        url = "http://chickenbones.net/maven/"
    }
}

dependencies {
    // 添加必要的依赖
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }
        
    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}
